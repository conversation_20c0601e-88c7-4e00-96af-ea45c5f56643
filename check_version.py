# 文件名: advanced_check.py

import sys
import ezdxf
import os

print("--- 高级环境诊断工具 ---")

try:
    print(f"Python 可执行文件路径: {sys.executable}")
    print(f"检测到的 ezdxf 库版本: {ezdxf.__version__}")
    
    # 打印 ezdxf 库的实际文件位置
    ezdxf_path = os.path.dirname(ezdxf.__file__)
    print(f"ezdxf 库实际加载路径: {ezdxf_path}")

    print("\nPython 模块搜索路径 (sys.path):")
    for path in sys.path:
        print(f"  - {path}")

    from ezdxf.addons.drawing import DrawingContext
    print("\n[诊断结果]: 成功导入 DrawingContext。您的 ezdxf 环境【已修复正常】。")

except ImportError:
    print("\n[诊断结果]: 无法导入 DrawingContext。您的 ezdxf 环境【仍有问题】。")
except Exception as e:
    print(f"\n[诊断结果]: 出现未知错误: {e}")


print("\n--- 诊断结束 ---")