import ezdxf
import os
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 导入 ezdxf 绘图模块
# 根据您成功的代码，我们使用 RenderContext
from ezdxf.addons.drawing import RenderContext, Frontend
from ezdxf.addons.drawing.matplotlib import MatplotlibBackend
from ezdxf.entities import MText, Dimension

# --- 用户可配置参数 ---
DXF_FILE = "测试轴.DXF"
OUTPUT_PNG_FILE = "annotated_drawing.png"
IMAGE_DPI = 300

def extract_and_locate_dimensions(file_path: str):
    """
    步骤一：从DXF文件中提取所有标注，并记录其值、类型、文本位置和边界框信息。
    """
    all_dimensions = []
    try:
        doc = ezdxf.readfile(file_path)
    except (IOError, ezdxf.DXFStructureError):
        return None  # 在主程序中处理错误信息

    for layout in doc.layouts:
        for dim in layout.query('DIMENSION'):
            try:
                virtual_ents = list(dim.virtual_entities())
                full_text = ""
                text_position = None
                text_bbox = None
                dimension_lines = []
                extension_lines = []

                # 收集所有虚拟实体信息
                for v_entity in virtual_ents:
                    if isinstance(v_entity, MText):
                        full_text += v_entity.text.strip()
                        if text_position is None:
                            text_position = v_entity.dxf.insert
                            # 尝试获取文本的边界框信息
                            if hasattr(v_entity.dxf, 'width') and hasattr(v_entity.dxf, 'height'):
                                text_bbox = {
                                    'width': v_entity.dxf.width if v_entity.dxf.width > 0 else len(full_text) * 3,
                                    'height': v_entity.dxf.height if v_entity.dxf.height > 0 else 8
                                }
                            else:
                                # 根据文本长度估算边界框
                                clean_text = full_text.replace("%%c", "ø")
                                text_bbox = {
                                    'width': len(clean_text) * 8,  # 每个字符约8个单位宽
                                    'height': 12  # 固定高度
                                }
                    elif hasattr(v_entity, 'dxf') and hasattr(v_entity.dxf, 'start') and hasattr(v_entity.dxf, 'end'):
                        # 这是一条线段（可能是尺寸线或延伸线）
                        line_info = {
                            'start': v_entity.dxf.start,
                            'end': v_entity.dxf.end,
                            'type': v_entity.dxftype()
                        }
                        # 简单判断：如果线段接近文本位置，可能是尺寸线
                        if text_position:
                            line_center = ((v_entity.dxf.start.x + v_entity.dxf.end.x) / 2,
                                         (v_entity.dxf.start.y + v_entity.dxf.end.y) / 2)
                            dist_to_text = ((line_center[0] - text_position.x)**2 +
                                          (line_center[1] - text_position.y)**2)**0.5
                            if dist_to_text < 50:  # 阈值可调整
                                dimension_lines.append(line_info)
                            else:
                                extension_lines.append(line_info)
                        else:
                            dimension_lines.append(line_info)

                if not full_text or text_position is None:
                    continue

                # 如果没有获取到边界框，使用默认估算
                if text_bbox is None:
                    clean_text = full_text.replace("%%c", "ø")
                    text_bbox = {
                        'width': len(clean_text) * 8,
                        'height': 12
                    }

                dim_info = {
                    "value": full_text,
                    "pos": text_position,
                    "bbox": text_bbox,
                    "type": "linear",
                    "dimension_lines": dimension_lines,
                    "extension_lines": extension_lines
                }
                if "%%c" in full_text:
                    dim_info["type"] = "diameter"
                all_dimensions.append(dim_info)
            except Exception as e:
                print(f"处理尺寸时出错: {e}")
                continue
    return all_dimensions

def calculate_optimal_label_position(text_pos, dimension_lines, extension_lines, ax_limits):
    """
    计算标号的最佳位置，避免覆盖原有尺寸
    """
    x_range = ax_limits['x_max'] - ax_limits['x_min']
    y_range = ax_limits['y_max'] - ax_limits['y_min']

    # 基础偏移量
    base_offset = min(x_range, y_range) * 0.02

    # 尝试多个位置：上、下、左、右、对角线方向
    candidate_positions = [
        (text_pos.x, text_pos.y + base_offset * 2),  # 上方
        (text_pos.x, text_pos.y - base_offset * 2),  # 下方
        (text_pos.x + base_offset * 2, text_pos.y),  # 右方
        (text_pos.x - base_offset * 2, text_pos.y),  # 左方
        (text_pos.x + base_offset * 1.5, text_pos.y + base_offset * 1.5),  # 右上
        (text_pos.x - base_offset * 1.5, text_pos.y + base_offset * 1.5),  # 左上
        (text_pos.x + base_offset * 1.5, text_pos.y - base_offset * 1.5),  # 右下
        (text_pos.x - base_offset * 1.5, text_pos.y - base_offset * 1.5),  # 左下
    ]

    # 选择距离尺寸线最远的位置
    best_pos = candidate_positions[0]
    max_min_distance = 0

    for pos in candidate_positions:
        min_distance = float('inf')

        # 计算到所有尺寸线的最小距离
        for line in dimension_lines:
            dist = point_to_line_distance(pos, line['start'], line['end'])
            min_distance = min(min_distance, dist)

        # 选择最小距离最大的位置
        if min_distance > max_min_distance:
            max_min_distance = min_distance
            best_pos = pos

    return best_pos

def point_to_line_distance(point, line_start, line_end):
    """
    计算点到线段的距离
    """
    x0, y0 = point
    x1, y1 = line_start.x, line_start.y
    x2, y2 = line_end.x, line_end.y

    # 线段长度
    line_length = ((x2 - x1)**2 + (y2 - y1)**2)**0.5
    if line_length == 0:
        return ((x0 - x1)**2 + (y0 - y1)**2)**0.5

    # 计算点到直线的距离
    distance = abs((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1) / line_length
    return distance

def draw_text_highlight(ax, text_pos, text_bbox, color='red', linewidth=2):
    """
    在尺寸数字文本周围绘制边框
    """
    # 使用实际的文本边界框信息
    text_width = text_bbox['width']
    text_height = text_bbox['height']

    # 添加适当的边距
    margin_x = 3
    margin_y = 2
    text_width += margin_x * 2
    text_height += margin_y * 2

    # 计算文本边界框（以文本位置为中心）
    min_x = text_pos.x - text_width / 2
    min_y = text_pos.y - text_height / 2

    # 绘制边框矩形（只有边框，无填充）
    rect = patches.Rectangle(
        (min_x, min_y),
        text_width,
        text_height,
        linewidth=linewidth,
        edgecolor=color,
        facecolor='none',  # 无填充色
        zorder=500  # 确保框在图形上方
    )
    ax.add_patch(rect)

    # 添加调试信息（可选）
    print(f"绘制文本框: 位置({text_pos.x:.1f}, {text_pos.y:.1f}), 大小({text_width:.1f}x{text_height:.1f})")

def draw_and_annotate(file_path: str, dimensions_data: list, output_filename: str):
    """
    步骤二：结合您成功的绘图代码，进行绘图并在图上添加编号标注和尺寸高亮。
    """
    try:
        # 1. 加载文件并设置 Matplotlib 绘图环境 (来自您的代码)
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_axes([0, 0, 1, 1])
        ax.set_aspect('equal')

        # 2. 使用 RenderContext 进行绘图 (来自您的代码 - 这是成功的关键！)
        ctx = RenderContext(doc)
        out = MatplotlibBackend(ax)
        frontend = Frontend(ctx, out)
        frontend.draw_layout(msp, finalize=True)
        print(f"DXF 文件 '{file_path}' 基础图形渲染成功。")

        # 获取坐标轴范围
        ax_limits = {
            'x_min': ax.get_xlim()[0],
            'x_max': ax.get_xlim()[1],
            'y_min': ax.get_ylim()[0],
            'y_max': ax.get_ylim()[1]
        }

        # 3. 在绘制好的图上，添加尺寸数字框选和编号标注
        print("正在添加尺寸数字框选和编号...")
        for i, dim_info in enumerate(dimensions_data, 1):
            # 绘制尺寸数字边框
            draw_text_highlight(ax, dim_info["pos"], dim_info["bbox"])

            # 计算最佳标号位置
            optimal_pos = calculate_optimal_label_position(
                dim_info["pos"],
                dim_info.get("dimension_lines", []),
                dim_info.get("extension_lines", []),
                ax_limits
            )

            # 定义标注样式：白色数字，红色圆形背景
            bbox_props = dict(boxstyle="circle,pad=0.3", fc="red", ec="darkred", lw=2)

            # 添加标号
            ax.text(optimal_pos[0], optimal_pos[1], str(i),
                    ha="center", va="center", color="white",
                    fontweight="bold", fontsize=10, bbox=bbox_props, zorder=1000)

            # 添加连接线（从标号到原始尺寸位置）
            ax.plot([optimal_pos[0], dim_info["pos"].x],
                   [optimal_pos[1], dim_info["pos"].y],
                   'r--', linewidth=1, alpha=0.7, zorder=999)

        # 4. 保存最终的带标注图片 (来自您的代码)
        print(f"正在将带标注的图形保存为 '{output_filename}'...")
        fig.savefig(output_filename, dpi=IMAGE_DPI, bbox_inches='tight')
        print("所有任务完成！")

    except Exception as e:
        print(f"在绘图或标注过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        plt.close(fig)


if __name__ == '__main__':
    # 首先，提取所有尺寸数据
    dimension_data_list = extract_and_locate_dimensions(DXF_FILE)

    if dimension_data_list:
        print("✅ 成功提取到所有尺寸实例，并已进行编号:\n")
        
        # 打印带编号的列表
        for i, dim_data in enumerate(dimension_data_list, 1):
            value_text = dim_data['value'].replace("%%c", "ø")
            type_text = "直径" if dim_data['type'] == 'diameter' else "长度"
            pos = dim_data['pos']
            bbox = dim_data['bbox']
            print(f"  编号 {i}: {value_text}  (类型: {type_text})")
            print(f"    位置: ({pos.x:.2f}, {pos.y:.2f}), 边界框: {bbox['width']:.1f}x{bbox['height']:.1f}")
        
        print("-" * 20)
        
        # 然后，调用绘图与标注函数
        draw_and_annotate(DXF_FILE, dimension_data_list, OUTPUT_PNG_FILE)
    else:
        print(f"错误：未能从文件 '{DXF_FILE}' 中提取到任何尺寸数据。")