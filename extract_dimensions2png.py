import ezdxf
import os
import matplotlib.pyplot as plt

# 导入 ezdxf 绘图模块
# 根据您成功的代码，我们使用 RenderContext
from ezdxf.addons.drawing import RenderContext, Frontend
from ezdxf.addons.drawing.matplotlib import MatplotlibBackend
from ezdxf.entities import MText, Dimension

# --- 用户可配置参数 ---
DXF_FILE = "测试轴.DXF"
OUTPUT_PNG_FILE = "annotated_drawing.png"
IMAGE_DPI = 300

def extract_and_locate_dimensions(file_path: str):
    """
    步骤一：从DXF文件中提取所有标注，并记录其值、类型和文本位置。
    (此函数是之前已调试成功的版本)
    """
    all_dimensions = []
    try:
        doc = ezdxf.readfile(file_path)
    except (IOError, ezdxf.DXFStructureError):
        return None  # 在主程序中处理错误信息

    for layout in doc.layouts:
        for dim in layout.query('DIMENSION'):
            try:
                virtual_ents = list(dim.virtual_entities())
                full_text = ""
                text_position = None
                for v_entity in virtual_ents:
                    if isinstance(v_entity, MText):
                        full_text += v_entity.text.strip()
                        if text_position is None:
                            text_position = v_entity.dxf.insert
                if not full_text or text_position is None:
                    continue
                dim_info = {"value": full_text, "pos": text_position, "type": "linear"}
                if "%%c" in full_text:
                    dim_info["type"] = "diameter"
                all_dimensions.append(dim_info)
            except Exception:
                continue
    return all_dimensions

def draw_and_annotate(file_path: str, dimensions_data: list, output_filename: str):
    """
    步骤二：结合您成功的绘图代码，进行绘图并在图上添加编号标注。
    """
    try:
        # 1. 加载文件并设置 Matplotlib 绘图环境 (来自您的代码)
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        fig = plt.figure()
        ax = fig.add_axes([0, 0, 1, 1])
        ax.set_aspect('equal')

        # 2. 使用 RenderContext 进行绘图 (来自您的代码 - 这是成功的关键！)
        ctx = RenderContext(doc)
        out = MatplotlibBackend(ax)
        frontend = Frontend(ctx, out)
        frontend.draw_layout(msp, finalize=True)
        print(f"DXF 文件 '{file_path}' 基础图形渲染成功。")

        # 3. 在绘制好的图上，添加我们的编号标注 (我们之前的标注逻辑)
        print("正在添加尺寸编号...")
        for i, dim_info in enumerate(dimensions_data, 1):
            pos = dim_info["pos"]
            
            # 定义标注样式：白色数字，红色圆形背景
            bbox_props = dict(boxstyle="circle,pad=0.2", fc="red", ec="red", lw=1)
            
            # 动态计算偏移量，避免遮挡原尺寸
            x_range = ax.get_xlim()[1] - ax.get_xlim()[0]
            offset = x_range * 0.015
            
            ax.text(pos.x, pos.y + offset, str(i), 
                    ha="center", va="center", color="white",
                    fontweight="bold", fontsize=8, bbox=bbox_props)

        # 4. 保存最终的带标注图片 (来自您的代码)
        print(f"正在将带标注的图形保存为 '{output_filename}'...")
        fig.savefig(output_filename, dpi=IMAGE_DPI)
        print("所有任务完成！")

    except Exception as e:
        print(f"在绘图或标注过程中发生错误: {e}")
    finally:
        plt.close(fig)


if __name__ == '__main__':
    # 首先，提取所有尺寸数据
    dimension_data_list = extract_and_locate_dimensions(DXF_FILE)

    if dimension_data_list:
        print("✅ 成功提取到所有尺寸实例，并已进行编号:\n")
        
        # 打印带编号的列表
        for i, dim_data in enumerate(dimension_data_list, 1):
            value_text = dim_data['value'].replace("%%c", "ø")
            type_text = "直径" if dim_data['type'] == 'diameter' else "长度"
            print(f"  编号 {i}: {value_text}  (类型: {type_text})")
        
        print("-" * 20)
        
        # 然后，调用绘图与标注函数
        draw_and_annotate(DXF_FILE, dimension_data_list, OUTPUT_PNG_FILE)
    else:
        print(f"错误：未能从文件 '{DXF_FILE}' 中提取到任何尺寸数据。")