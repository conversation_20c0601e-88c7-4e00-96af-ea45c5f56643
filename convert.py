import cv2
import numpy as np

def preprocess_image(image_path, output_path=None, do_perspective=False, pts=None):
    # 1. 读取图片
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"未找到图片: {image_path}")

    # 2. 灰度化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 3. 对比度增强（CLAHE）
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)

    # 4. 锐化
    # kernel = np.array([[0, -0.5, 0],
    #                    [-0.5, 3,-0.5],
    #                    [0, -0.5, 0]])
    # sharpened = cv2.filter2D(enhanced, -1, kernel)

    # 5. 去噪（中值滤波）
    # denoised = cv2.medianBlur(sharpened, 3)
    # denoised = cv2.<PERSON><PERSON><PERSON><PERSON>lur(enhanced, (5, 5), 0)
    denoised = enhanced

    # 6. 透视矫正（可选）
    if do_perspective and pts is not None:
        # pts: 源点，需为4个点，顺序为左上、右上、右下、左下
        pts = np.array(pts, dtype="float32")
        # 目标点
        w = max(np.linalg.norm(pts[0] - pts[1]), np.linalg.norm(pts[2] - pts[3]))
        h = max(np.linalg.norm(pts[0] - pts[3]), np.linalg.norm(pts[1] - pts[2]))
        dst = np.array([[0,0],[w-1,0],[w-1,h-1],[0,h-1]], dtype="float32")
        M = cv2.getPerspectiveTransform(pts, dst)
        denoised = cv2.warpPerspective(denoised, M, (int(w), int(h)))

    # 保存或返回结果
    if output_path:
        cv2.imwrite(output_path, denoised)
    return denoised

if __name__ == "__main__":
    # 示例用法
    img_path = "R.png"  # 替换为你的图片路径
    out_path = "output.jpg"
    # 如果需要透视矫正，手动输入四个点
    # pts = [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
    preprocess_image(img_path, out_path, do_perspective=False, pts=None)
    print("预处理完成，结果已保存。")
