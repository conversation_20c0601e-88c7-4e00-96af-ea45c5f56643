import ezdxf
import os
from ezdxf.entities import MText

def extract_dimensions_final(file_path):
    """
    从DXF文件中提取所有标注值，正确处理复杂的块状标注。
    此版本修正了布局遍历逻辑，确保每个标注只被检测一次。

    Args:
        file_path (str): DXF文件的路径。

    Returns:
        dict: 一个包含 'linear' 和 'diameter' 键的字典。
    """
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在。")
        return None

    try:
        doc = ezdxf.readfile(file_path)
    except (IOError, ezdxf.DXFStructureError) as e:
        print(f"错误: 无法加载或解析 '{file_path}'. 详情: {e}")
        return None

    dimensions = {
        "linear": [],
        "diameter": []
    }

    # ⭐⭐⭐ 最终逻辑修正 ⭐⭐⭐
    # 直接遍历 doc.layouts，这个迭代器已经包含了模型空间和所有图纸空间。
    # 无需手动添加 doc.modelspace()，从而避免重复。
    layouts = doc.layouts

    for layout in layouts:
        for dim in layout.query('DIMENSION'):
            try:
                virtual_ents = list(dim.virtual_entities())
                
                full_text = ""
                for v_entity in virtual_ents:
                    if isinstance(v_entity, MText):
                        full_text += v_entity.text.strip()

                if not full_text:
                    continue

                if "%%c" in full_text:
                    dimensions["diameter"].append(full_text)
                else:
                    try:
                        float(full_text)
                        dimensions["linear"].append(full_text)
                    except ValueError:
                        pass

            except Exception as e:
                print(f"警告: 无法处理一个标注 (句柄: {dim.dxf.handle}). 错误: {e}")
                continue

    return dimensions

if __name__ == '__main__':
    dxf_file_path = "测试轴.DXF"
    extracted_dims = extract_dimensions_final(dxf_file_path)

    if extracted_dims:
        print("✅ 成功从DXF文件中提取到标注！\n")

        print("线性标注 (长度):")
        if extracted_dims["linear"]:
            linear_vals = sorted(filter(None, extracted_dims["linear"]), key=float, reverse=True)
            print(f"  -> {', '.join(linear_vals)}")
        else:
            print("  -> 未找到线性标注。")

        print("-" * 20)

        print("直径标注:")
        if extracted_dims["diameter"]:
            diameter_vals = list(filter(None, extracted_dims["diameter"]))
            cleaned_diameters = [val.replace("%%c", "ø") for val in diameter_vals]
            unique_diameters = sorted(cleaned_diameters, key=lambda x: float(x.lstrip('ø')), reverse=True)
            print(f"  -> {', '.join(unique_diameters)}")
        else:
            print("  -> 未找到直径标注。")