import json
import time
import base64

import requests

# 1. 图片转base64的函数
def image_to_base64(image_path):
    """将本地图片转换为base64编码"""
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            return f"data:image/jpeg;base64,{encoded_string}"
    except FileNotFoundError:
        print(f"错误: 找不到图片文件 {image_path}")
        return None
    except Exception as e:
        print(f"错误: 读取图片文件时出错 - {e}")
        return None

def rotate_image_90_clockwise(image_path, output_path):
    """将图片顺时针旋转90度并保存"""
    try:
        from PIL import Image

        # 打开图片
        image = Image.open(image_path)

        # 顺时针旋转90度 (PIL中使用-90度表示顺时针旋转90度)
        rotated_image = image.rotate(-90, expand=True)

        # 保存旋转后的图片
        rotated_image.save(output_path, 'PNG')
        print(f"✓ 图片已顺时针旋转90度并保存为: {output_path}")
        return True

    except Exception as e:
        print(f"图片旋转失败: {e}")
        return False

def combine_detection_results_on_original(original_image_path,
                                        original_ocr_results, rotated_ocr_results,
                                        original_img_size, rotated_img_size,
                                        output_path="combined_results.png"):
    """将原图和旋转图的检测结果都标注在原图上"""
    try:
        import cv2
        import numpy as np
        from PIL import Image

        # 读取原图
        original_img = cv2.imread(original_image_path)

        if original_img is None:
            print("无法读取原图文件")
            return False

        orig_h, orig_w = original_img.shape[:2]

        # 定义颜色列表 (BGR格式)
        colors = [
            (0, 0, 255),    # 红色
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (255, 0, 255),  # 洋红色
            (128, 0, 128)  # 紫色
        ]

        # 绘制原图的检测结果
        for result in original_ocr_results:
            bbox = result['bbox']
            x1, y1, x2, y2 = map(int, bbox)

            color = colors[(result['number']-1) % len(colors)]

            # 绘制边界框
            cv2.rectangle(original_img, (x1, y1), (x2, y2), color, 2)

            # 绘制编号和OCR结果
            draw_number_and_ocr(original_img, result['number'], result['text'],
                               x1, y1, x2, y2, color)

        # 绘制旋转图的检测结果（需要将坐标转换回原图坐标系）
        for result in rotated_ocr_results:
            bbox = result['bbox']
            rot_x1, rot_y1, rot_x2, rot_y2 = map(int, bbox)

            # 将旋转图的坐标转换回原图坐标系
            # 让我们重新分析坐标转换：
            # 原图: 宽度W, 高度H
            # 顺时针旋转90度后: 宽度H, 高度W
            #
            # 顺时针旋转90度的变换: (x', y') = (H - 1 - y, x)
            # 逆变换（从旋转图回到原图）: (x, y) = (y', W - 1 - x')
            #
            # 但是我们需要考虑旋转图的实际尺寸

            # 原图的尺寸
            orig_h, orig_w = original_img_size
            # 旋转图的尺寸应该是 (orig_w, orig_h)

            # 从旋转图坐标转换回原图坐标
            # 经过分析，正确的逆变换公式应该是:
            # 原图点(x,y) 顺时针旋转90度后变为 (orig_h-1-y, x)
            # 所以旋转图点(rot_x, rot_y) 对应的原图点为 (rot_y, orig_h-1-rot_x)
            # 但是考虑到边界框，我们需要正确处理两个角点

            # 旋转图的尺寸是 orig_w × orig_h (宽×高)
            # 转换公式: 旋转图(rot_x, rot_y) -> 原图(rot_y, orig_h-1-rot_x)

            orig_x1 = rot_y1
            orig_y1 = orig_h - 1 - rot_x2
            orig_x2 = rot_y2
            orig_y2 = orig_h - 1 - rot_x1

            # 确保坐标顺序正确 (x1 < x2, y1 < y2)
            if orig_x1 > orig_x2:
                orig_x1, orig_x2 = orig_x2, orig_x1
            if orig_y1 > orig_y2:
                orig_y1, orig_y2 = orig_y2, orig_y1

            # 调试信息
            print(f"  坐标转换: 旋转图[{rot_x1},{rot_y1},{rot_x2},{rot_y2}] -> 原图[{orig_x1},{orig_y1},{orig_x2},{orig_y2}]")
            print(f"  原图尺寸: {orig_h}x{orig_w}")
            print(f"  OCR文字: {result['text']}")

            # 确保坐标在原图范围内
            orig_x1 = max(0, min(orig_w, orig_x1))
            orig_y1 = max(0, min(orig_h, orig_y1))
            orig_x2 = max(0, min(orig_w, orig_x2))
            orig_y2 = max(0, min(orig_h, orig_y2))

            # 为旋转图的编号添加偏移，避免与原图重复
            number = result['number'] + len(original_ocr_results)
            color = colors[(number-1) % len(colors)]

            # 绘制边界框（使用虚线样式区分）
            # 由于OpenCV不直接支持虚线，我们用不同的线宽来区分
            cv2.rectangle(original_img, (orig_x1, orig_y1), (orig_x2, orig_y2), color, 3)

            # 绘制编号和OCR结果
            draw_number_and_ocr(original_img, number, result['text'],
                               orig_x1, orig_y1, orig_x2, orig_y2, color)

        # 保存结果
        cv2.imwrite(output_path, original_img)
        print(f"✓ 汇总结果已保存为: {output_path}")
        return True

    except Exception as e:
        print(f"结果汇总失败: {e}")
        return False

def draw_number_and_ocr(image, number, ocr_text, x1, y1, x2, y2, color):
    """在图像上绘制编号和OCR文本"""
    import cv2
    # 计算编号圆圈位置（在边界框外侧）
    circle_radius = 12
    margin = 8

    # 优先放在左上角外侧
    circle_x = x1 - circle_radius - margin
    circle_y = y1 - circle_radius - margin

    # 边界检查和位置调整
    if circle_x < circle_radius:
        circle_x = x2 + circle_radius + margin  # 右侧
    if circle_y < circle_radius:
        circle_y = y2 + circle_radius + margin  # 下方
    if circle_x > image.shape[1] - circle_radius:
        circle_x = x1 - circle_radius - margin  # 回到左侧
        circle_y = y1 + circle_radius + margin  # 稍微下移
    if circle_y > image.shape[0] - circle_radius:
        circle_y = y1 - circle_radius - margin  # 回到上方

    circle_center = (circle_x, circle_y)

    # 绘制编号圆圈
    cv2.circle(image, circle_center, circle_radius, color, -1)
    cv2.circle(image, circle_center, circle_radius, (0, 0, 0), 1)

    # 绘制编号文字
    font_scale = 0.5 if number < 10 else 0.4
    text_offset = 6 if number < 10 else 8
    cv2.putText(image, str(number),
               (circle_center[0] - text_offset, circle_center[1] + 4),
               cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), 1)

    # 只在有OCR结果时显示OCR文本
    if ocr_text and ocr_text != "未识别到文字":
        # 计算OCR文本位置（在编号圆圈旁边）
        text_x = circle_center[0] + circle_radius + 5
        text_y = circle_center[1] + 4

        # 边界检查
        ocr_label = f"OCR: {ocr_text}"
        (text_width, text_height), _ = cv2.getTextSize(ocr_label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)

        if text_x + text_width > image.shape[1]:
            text_x = circle_center[0] - text_width - circle_radius - 5

        # 绘制OCR文本背景
        cv2.rectangle(image, (text_x - 2, text_y - text_height - 2),
                     (text_x + text_width + 2, text_y + 2), (0, 0, 0), -1)

        # 绘制OCR文字
        cv2.putText(image, ocr_label, (text_x, text_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
def extract_text_with_easyocr(image_path, bbox):
    """使用EasyOCR提取文字"""
    print("使用EasyOCR进行文字识别...")
    try:
        import easyocr
        from PIL import Image

        # 读取图片
        image = Image.open(image_path)

        # 如果是RGBA模式，转换为RGB
        if image.mode == 'RGBA':
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # 扩大裁剪区域以提高OCR识别率
        x1, y1, x2, y2 = map(int, bbox)

        # 计算扩展边距（扩大30%，比Tesseract更大）
        width = x2 - x1
        height = y2 - y1
        margin_x = max(8, int(width * 0.3))  # 至少8像素，或宽度的30%
        margin_y = max(8, int(height * 0.3))  # 至少8像素，或高度的30%

        # 扩展边界框，但不超出图片范围
        img_width, img_height = image.size
        expanded_x1 = max(0, x1 - margin_x)
        expanded_y1 = max(0, y1 - margin_y)
        expanded_x2 = min(img_width, x2 + margin_x)
        expanded_y2 = min(img_height, y2 + margin_y)

        # 裁剪扩展后的区域
        cropped = image.crop((expanded_x1, expanded_y1, expanded_x2, expanded_y2))

        print(f"  EasyOCR扩展框: [{x1},{y1},{x2},{y2}] -> [{expanded_x1},{expanded_y1},{expanded_x2},{expanded_y2}]")

        # 图像预处理 - 提高OCR准确率
        # 放大图像以提高识别率
        width, height = cropped.size
        min_size = 200  # EasyOCR的最小尺寸设置更大
        if width < min_size or height < 50:
            scale_x = max(2, min_size // width)
            scale_y = max(2, 50 // height)
            scale = max(scale_x, scale_y, 2)  # 至少放大2倍
            new_size = (width * scale, height * scale)
            cropped = cropped.resize(new_size, Image.LANCZOS)
            print(f"  EasyOCR图像放大: {width}x{height} -> {new_size[0]}x{new_size[1]} (放大{scale}倍)")

        # 创建临时图片路径（不保存到磁盘）
        temp_path = "temp_easyocr_crop.png"
        cropped.save(temp_path, 'PNG')

        # 初始化OCR
        reader = easyocr.Reader(['ch_sim', 'en'],gpu=True)  # 支持中文和英文

        # 识别文字
        results = reader.readtext(temp_path)

        # 提取文字内容
        texts = []
        for (_, text, confidence) in results:
            if confidence > 0.5:  # 置信度阈值
                texts.append(text.strip())

        # 清理临时文件
        try:
            import os
            os.remove(temp_path)
        except:
            pass

        return " ".join(texts) if texts else "未识别到文字"

    except ImportError:
        raise Exception("EasyOCR未安装")
    except Exception as e:
        raise Exception(f"EasyOCR识别失败: {e}")

# 2. 封装 Header，带上 Token
headers = {
    "Content-Type": "application/json",
    "Token"       : "04e825b1409bda11fc511d7c9e983fa7"
}

# 3. 发起DINO-X算法调用
# 读取本地图片并转换为base64
image_path = "111.png"
print(f"正在读取图片: {image_path}")
image_base64 = image_to_base64(image_path)

if image_base64 is None:
    print("图片读取失败，程序退出")
    exit(1)

print("图片读取成功，开始调用API...")

# 配置DINO-X检测参数
api_body = {
    "model": "DINO-X-1.0",  # 使用DINO-X 1.0模型
    "image": image_base64,  # 使用本地图片的base64编码
    "prompt": {
        "type": "text",
        "text": "dimension number"  
    },
    "targets": ["bbox", "mask"],  # 返回边界框和掩码
    "bbox_threshold": 0.25,  # 边界框置信度阈值
    "iou_threshold": 0.8,    # IoU阈值
    "mask_format": "coco_rle"  # 掩码格式
}

resp = requests.post(
    url='https://api.deepdataspace.com/v2/task/dinox/detection',
    json=api_body,
    headers=headers
)
json_resp = resp.json()
print("任务创建响应:", json_resp)

# 4. 获取 task_uuid
if json_resp.get("code") == 0:
    task_uuid = json_resp["data"]["task_uuid"]
    print(f"任务UUID: {task_uuid}")

    # 5. 轮询任务状态
    print("正在等待任务完成...")
    while True:
        resp = requests.get(f'https://api.deepdataspace.com/v2/task_status/{task_uuid}', headers=headers)
        json_resp = resp.json()
        status = json_resp["data"]["status"]
        print(f"当前状态: {status}")

        if status not in ["waiting", "running"]:
            break
        time.sleep(2)  # 每2秒检查一次状态

    # 6. 处理结果
    if json_resp["data"]["status"] == "failed":
        print("任务失败:")
        print(json.dumps(json_resp, indent=2, ensure_ascii=False))
    elif json_resp["data"]["status"] == "success":
        print("任务成功完成!")
        # print("检测结果:")
        result = json_resp["data"]["result"]
        # print(result)

        # 解析检测结果 - 尝试多种可能的结果格式
        detections = None

        # 尝试不同的结果格式
        if "objects" in result:
            print("检测结果格式为: objects")
            detections = result["objects"]

        if detections and len(detections) > 0:
            print(f"检测到 {len(detections)} 个目标:")

            # 保存原图的检测结果
            original_ocr_results = []
            print("\n=== 原图检测结果 ===")
            for i, detection in enumerate(detections):
                bbox = (detection.get('bbox') or
                       detection.get('box') or
                       detection.get('bounding_box'))

                if bbox:
                    print(f"正在识别第 {i+1} 个目标的文字...")
                    ocr_text = extract_text_with_easyocr(image_path, bbox)
                    print(f"  编号 {i+1}: {ocr_text}")

                    original_ocr_results.append({
                        'number': i + 1,
                        'bbox': bbox,
                        'text': ocr_text,
                        'detection': detection
                    })

            # 旋转图片并进行第二次检测（不保存旋转图片到磁盘）
            print("\n=== 开始旋转图片检测 ===")

            # 创建临时旋转图片
            rotated_image_path = "temp_rotated.png"

            if rotate_image_90_clockwise(image_path, rotated_image_path):
                print(f"开始对旋转图片进行检测...")

                # 读取旋转后的图片并转换为base64
                rotated_image_base64 = image_to_base64(rotated_image_path)

                if rotated_image_base64:
                    # 配置DINO-X检测参数（旋转图片）
                    rotated_api_body = {
                        "model": "DINO-X-1.0",
                        "image": rotated_image_base64,
                        "prompt": {
                            "type": "text",
                            "text": "dimension number"
                        },
                        "targets": ["bbox", "mask"],
                        "bbox_threshold": 0.25,
                        "iou_threshold": 0.8,
                        "mask_format": "coco_rle"
                    }

                    # 发起API调用
                    resp = requests.post(
                        url='https://api.deepdataspace.com/v2/task/dinox/detection',
                        json=rotated_api_body,
                        headers=headers
                    )
                    json_resp = resp.json()
                    print("旋转图片任务创建响应:", json_resp)

                    if json_resp.get("code") == 0:
                        task_uuid = json_resp["data"]["task_uuid"]
                        print(f"旋转图片任务UUID: {task_uuid}")

                        # 轮询任务状态
                        print("正在等待旋转图片任务完成...")
                        while True:
                            resp = requests.get(f'https://api.deepdataspace.com/v2/task_status/{task_uuid}', headers=headers)
                            json_resp = resp.json()
                            status = json_resp["data"]["status"]
                            print(f"旋转图片任务状态: {status}")

                            if status not in ["waiting", "running"]:
                                break
                            time.sleep(2)

                        # 处理旋转图片的检测结果
                        if json_resp["data"]["status"] == "success":
                            print("旋转图片检测成功!")
                            result = json_resp["data"]["result"]

                            # 解析检测结果
                            rotated_detections = None
                            if "objects" in result:
                                rotated_detections = result["objects"]

                            if rotated_detections and len(rotated_detections) > 0:
                                print(f"旋转图片检测到 {len(rotated_detections)} 个目标:")

                                # 保存旋转图的检测结果
                                rotated_ocr_results = []
                                print("\n=== 旋转图检测结果 ===")
                                for i, detection in enumerate(rotated_detections):
                                    bbox = (detection.get('bbox') or
                                           detection.get('box') or
                                           detection.get('bounding_box'))

                                    if bbox:
                                        print(f"正在识别旋转图第 {i+1} 个目标的文字...")
                                        ocr_text = extract_text_with_easyocr(rotated_image_path, bbox)
                                        print(f"  编号 {i+1}: {ocr_text}")

                                        rotated_ocr_results.append({
                                            'number': i + 1,
                                            'bbox': bbox,
                                            'text': ocr_text,
                                            'detection': detection
                                        })

                                # 汇总两次检测的结果到原图上
                                print("\n=== 汇总检测结果 ===")

                                # 获取图片尺寸信息
                                import cv2
                                original_img = cv2.imread(image_path)
                                rotated_img = cv2.imread(rotated_image_path)
                                original_img_size = original_img.shape[:2]
                                rotated_img_size = rotated_img.shape[:2]

                                if combine_detection_results_on_original(
                                    image_path,
                                    original_ocr_results, rotated_ocr_results,
                                    original_img_size, rotated_img_size,
                                    "combined_results.png"
                                ):
                                    print("✓ 所有检测结果已汇总到原图上")
                                    print(f"原图检测到 {len(original_ocr_results)} 个目标")
                                    print(f"旋转图检测到 {len(rotated_ocr_results)} 个目标")
                                    print(f"总计检测到 {len(original_ocr_results) + len(rotated_ocr_results)} 个目标")

                                    # 暂时保留临时文件用于调试
                                    print(f"✓ 旋转图片保存为: {rotated_image_path}")
                                    # try:
                                    #     import os
                                    #     os.remove(rotated_image_path)
                                    #     print("✓ 临时文件已清理")
                                    # except:
                                    #     pass
                                else:
                                    print("✗ 结果汇总失败")
                            else:
                                print("旋转图片未检测到目标")
                        else:
                            print("旋转图片检测失败")
                    else:
                        print("旋转图片任务创建失败")
                else:
                    print("旋转图片base64编码失败")
            else:
                print("图片旋转失败")
        else:
            print("未检测到目标或结果格式不识别")
            print("完整结果结构:")
            print("结果类型:", type(result))
            print("结果键值:", list(result.keys()) if isinstance(result, dict) else "非字典类型")
            # print(json.dumps(result, indent=2, ensure_ascii=False))
else:
    print("任务创建失败:")
    print(json.dumps(json_resp, indent=2, ensure_ascii=False))

