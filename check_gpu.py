import torch

# 核心诊断代码
is_available = torch.cuda.is_available()

print(f"PyTorch can access CUDA: {is_available}")

# 如果可用，打印更多信息
if is_available:
    print(f"Number of CUDA devices: {torch.cuda.device_count()}")
    print(f"Current device name: {torch.cuda.get_device_name(0)}")
else:
    print("\n[Error] PyTorch cannot find a usable CUDA environment.")
    print("This is the root cause of the EasyOCR warning.")
    print("Please proceed to Step 3 to fix the PyTorch installation.")