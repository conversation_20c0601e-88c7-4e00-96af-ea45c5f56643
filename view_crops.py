#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看裁剪图片的脚本
"""

import os
from PIL import Image

def view_crop_info():
    """显示所有裁剪图片的信息"""
    print("扩展后的裁剪图片信息:")
    print("="*60)
    
    # 获取所有扩展后的裁剪图片
    expanded_files = [f for f in os.listdir('.') if f.startswith('crop_expanded_') and f.endswith('.jpg')]
    expanded_files.sort()
    
    for i, filename in enumerate(expanded_files, 1):
        try:
            img = Image.open(filename)
            width, height = img.size
            print(f"编号 {i:2d}: {filename}")
            print(f"        尺寸: {width}x{height} 像素")
            
            # 分析图像亮度
            gray = img.convert('L')
            pixels = list(gray.getdata())
            avg_brightness = sum(pixels) / len(pixels)
            
            if avg_brightness < 100:
                brightness_desc = "较暗"
            elif avg_brightness > 200:
                brightness_desc = "较亮"
            else:
                brightness_desc = "中等亮度"
            
            print(f"        亮度: {avg_brightness:.1f} ({brightness_desc})")
            print()
            
        except Exception as e:
            print(f"无法读取 {filename}: {e}")
    
    print(f"总共生成了 {len(expanded_files)} 个扩展裁剪图片")
    print("\n建议:")
    print("1. 手动查看这些图片，确认是否包含清晰的文字")
    print("2. 如果图片中有文字但OCR未识别，可能需要:")
    print("   - 安装更好的OCR引擎 (pip install easyocr)")
    print("   - 调整图像预处理参数")
    print("   - 检查文字语言设置")

if __name__ == "__main__":
    view_crop_info()
