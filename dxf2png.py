import ezdxf
from ezdxf.addons.drawing import RenderContext, Frontend
from ezdxf.addons.drawing.matplotlib import MatplotlibBackend
import matplotlib.pyplot as plt

# --- 用户可配置参数 ---
DXF_FILE = "测试轴.DXF"  # <--- 替换为你的DXF文件名
PNG_FILE = "output_drawing.png" # <--- 你想要保存的PNG文件名
IMAGE_DPI = 300                 # <--- 输出图像的DPI（分辨率），300是常见的印刷质量
BACKGROUND_COLOR = "#FFFFFF"    # <--- 设置图像背景色 (FFFFFF 是白色)

# --------------------

try:
    # 1. 加载DXF文件
    doc = ezdxf.readfile(DXF_FILE)
    msp = doc.modelspace()  # 获取模型空间

    # 2. 设置 Matplotlib 绘图环境
    fig = plt.figure()
    # 创建一个占据整个图窗的坐标轴
    ax = fig.add_axes([0, 0, 1, 1])
    ax.set_aspect('equal') # 保证图形的纵横比正确

    # 3. 创建 ezdxf 的渲染前端和后端
    ctx = RenderContext(doc)
    
    # 配置前端，例如设置背景颜色
    # 如果DXF文件本身有背景色设置，可以取消下面这行注释来覆盖它
    # ctx.set_current_layout(msp)
    # ctx.current_layout_properties.set_colors(bg_color=BACKGROUND_COLOR)

    out = MatplotlibBackend(ax)
    
    # 4. 执行绘图
    # 前端负责处理DXF实体，后端 (MatplotlibBackend) 负责将它们绘制出来
    frontend = Frontend(ctx, out)
    frontend.draw_layout(msp, finalize=True)

    # 5. 保存为PNG文件
    # MatplotlibBackend 会自动设置合适的视图范围，所以我们直接保存即可
    print(f"正在将 '{DXF_FILE}' 渲染为 '{PNG_FILE}'...")
    fig.savefig(PNG_FILE, dpi=IMAGE_DPI)
    print("渲染完成！")

except IOError:
    print(f"错误：无法找到或读取文件 '{DXF_FILE}'。")
except ezdxf.DXFStructureError:
    print(f"错误：'{DXF_FILE}' 不是一个有效的DXF文件或文件已损坏。")
finally:
    # 关闭 matplotlib 图窗以释放内存
    plt.close(fig)